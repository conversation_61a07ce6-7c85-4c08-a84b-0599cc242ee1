import {
  Column,
  DynamicDataTable,
  StatusBadge,
} from "@/components/constant/dynamic-data-table";
import StatsCard from "@/components/constant/states-card";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { toDate } from "date-fns";
import dayjs from "dayjs";
import React, { useState } from "react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
} from "recharts";
interface Stat {
  title: string;
  value: number;
  change: number;
  isPositive: boolean;
}

const paymentModeData = [
  { name: "UPI", value: 45, color: "#8884d8" },
  { name: "Card", value: 30, color: "#82ca9d" },
  { name: "Net Banking", value: 20, color: "#ffc658" },
  { name: "Wallet", value: 5, color: "#ff7300" },
];

const statsData: Stat[] = [
  { title: "Total Test", value: 8452, change: 5, isPositive: true },
  { title: "Published Results", value: 1230, change: 3, isPositive: true },
  { title: "Total Scholars", value: 2345, change: 1, isPositive: false },
  { title: "Unpublished Result", value: 678, change: 8, isPositive: true },
];

const revenueData = [
  { month: "Jan", revenue: 245000, subscriptions: 120 },
  { month: "Feb", revenue: 289000, subscriptions: 145 },
  { month: "Mar", revenue: 312000, subscriptions: 167 },
  { month: "Apr", revenue: 298000, subscriptions: 156 },
  { month: "May", revenue: 334000, subscriptions: 189 },
  { month: "Jun", revenue: 367000, subscriptions: 203 },
];

interface UserLog {
  userId: string;
  name: string;
  role: "Admin" | "Content";
  time: string;
  operation: string;
}

// Dummy data
const sampleUserLogs: UserLog[] = [
  {
    userId: "001",
    name: "Adithyan Ullas",
    role: "Admin",
    time: "20-01-2024, 16:00:00",
    operation: "Generated something in creator team",
  },
  {
    userId: "002",
    name: "Swaroop",
    role: "Admin",
    time: "20-01-2024, 16:00:00",
    operation: "Generated something in creator team",
  },
  {
    userId: "003",
    name: "Kauishik",
    role: "Admin",
    time: "20-01-2024, 16:00:00",
    operation: "Collaborated on a new feature design",
  },
  {
    userId: "004",
    name: "Amar",
    role: "Admin",
    time: "20-01-2024, 16:00:00",
    operation: "Conducted user research and gathered insights",
  },
  {
    userId: "005",
    name: "Rahul",
    role: "Content",
    time: "20-01-2024, 16:00:00",
    operation: "Designed a prototype for testing",
  },
  {
    userId: "006",
    name: "Ganga",
    role: "Content",
    time: "20-01-2024, 16:00:00",
    operation: "Refined UI components for better usability",
  },
  {
    userId: "007",
    name: "Sree",
    role: "Content",
    time: "20-01-2024, 16:00:00",
    operation: "Presented findings to the stakeholders",
  },
  {
    userId: "008",
    name: "Aditi",
    role: "Content",
    time: "20-01-2024, 16:00:00",
    operation: "Collaborated with developers on new updates",
  },
];

// Columns
const userLogColumns: Column<UserLog>[] = [
  { key: "userId", label: "User ID", sortable: true },
  { key: "name", label: "Name", sortable: true },
  {
    key: "role",
    label: "Role",
    sortable: true,
    render: (value) => (
      <StatusBadge status={value} /> // ✅ same badge style (green/blue dots)
    ),
  },
  { key: "time", label: "Time", sortable: true },
  { key: "operation", label: "Operation", sortable: false },
];

export default function FinanceDashboard() {
  const [fromDate, setFromDate] = useState(
    dayjs().subtract(7, "day").format("YYYY-MM-DD")
  );
  const handleRowClick = (row: UserLog) => {
    console.log("Transaction clicked:", row.userId);
  };

  const [toDate, setToDate] = useState(dayjs().format("YYYY-MM-DD"));

  const handleFilter = () => {
    console.log("From:", fromDate);
    console.log("To:", toDate);
    // Add your filter logic here
  };
  return (
    <div className="mt-4">
      <div className="flex gap-4 mb-6">
        {/* Date filters side by side */}
        <div className="flex gap-4">
          <div className="flex flex-col w-1/2">
            <label className="text-gray-400 text-sm mb-1">From Date</label>
            <Input
              type="date"
              value={fromDate}
              onChange={(e) => setFromDate(e.target.value)}
            />
          </div>

          <div className="flex flex-col w-1/2">
            <label className="text-gray-400 text-sm mb-1">To Date</label>
            <Input
              type="date"
              value={toDate}
              onChange={(e) => setToDate(e.target.value)}
              max={dayjs().format("YYYY-MM-DD")}
            />
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {statsData.map((stat, index) => (
          <StatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            isPositive={stat.isPositive}
          />
        ))}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-4">
        <Card className="bg-white/5 backdrop-blur-md border-white/10 text-white">
          <CardHeader>
            <CardTitle>Payment Mode Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={paymentModeData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }: any) =>
                    `${name} ${(percent * 100).toFixed(0)}%`
                  }
                >
                  {paymentModeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        <Card className="bg-white/5 backdrop-blur-md border-white/10 text-white">
          <CardHeader>
            <CardTitle>Revenue Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={revenueData}>
                {/* Grid lines */}
                <CartesianGrid
                  stroke="rgba(255,255,255,0.25)" // Y-axis grid lines in white with 25% opacity
                  vertical={false} // optional: only show horizontal lines
                  strokeDasharray="3 3"
                />

                {/* X and Y axes */}
                <XAxis dataKey="month" stroke="rgba(255,255,255,0.7)" />
                <YAxis stroke="rgba(255,255,255,0.7)" />

                {/* Tooltip */}
                <Tooltip
                  contentStyle={{
                    backgroundColor: "rgba(0,0,0,0.8)",
                    border: "1px solid rgba(255,255,255,0.1)",
                    borderRadius: "8px",
                  }}
                />

                {/* Bars */}
                <Bar dataKey="revenue" fill="#52C3C5" barSize={24} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <div className="flex mt-2 ">
        <DynamicDataTable<UserLog>
          data={sampleUserLogs}
          columns={userLogColumns}
          onRowClick={handleRowClick}
        />
      </div>
    </div>
  );
}
